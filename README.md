# TradingBot 🚀

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![MetaTrader5](https://img.shields.io/badge/MetaTrader5-Integration-green.svg)](https://www.metatrader5.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Strategies](https://img.shields.io/badge/Strategies-3+-orange.svg)](#strategies)

A professional-grade algorithmic trading bot with **multi-strategy support**, **multi-timeframe analysis**, and **1:1 backtest-to-live consistency**. Built for MetaTrader 5 with a clean, extensible architecture that makes adding new Python trading strategies effortless.

## ✨ Key Features

- 🎯 **Multi-Strategy Architecture**: Easily add custom Python strategies
- ⏱️ **Multi-Timeframe Support**: Trade on any timeframe (M1, M5, M15, M30, H1, H4, D1)
- 🔄 **1:1 Backtest Consistency**: Identical results between backtest and live trading
- 📊 **Real-Time Tick Monitoring**: Advanced market microstructure analysis
- 🛡️ **Production Ready**: Comprehensive error handling and risk management
- 📈 **Interactive Visualization**: Professional charts and trade analysis

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/TradingBot.git
cd TradingBot

# Install dependencies
pip install -r requirements.txt

# Configure MT5 credentials in config.py
# Run your first backtest
python mainBot.py --backtest --strategy bollinger --timeframe M15 --days 1
```

### Basic Usage

```bash
# Backtest with different strategies and timeframes
python mainBot.py --backtest --strategy bollinger --timeframe M5 --days 3
python mainBot.py --backtest --strategy breakout --timeframe H1 --days 7

# Live trading with tick monitoring
python mainBot.py --strategy bollinger --timeframe M15 --tick-monitoring

# Validate consistency between backtest and live
python validate_consistency.py --strategy bollinger --timeframe M15
```

## 🏗️ Architecture

### Clean 3-Tier Design

```
┌─────────────────────────────────────────────────────────────┐
│                    Strategy Layer                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐   │
│  │  Bollinger  │ │  Breakout   │ │   Your Custom       │   │
│  │  Strategy   │ │  Strategy   │ │   Strategy Here     │   │
│  └─────────────┘ └─────────────┘ └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Core Trading Engine                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐   │
│  │   BaseTrader    │ │   Backtester    │ │ Tick Monitor│   │
│  │   (Live/Data)   │ │  (Simulation)   │ │ (Analysis)  │   │
│  └─────────────────┘ └─────────────────┘ └─────────────┘   │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  Broker Abstraction                         │
│  ┌─────────────────────────────────────────────────────┐   │
│  │              MetaTrader 5 Integration               │   │
│  │         (Real-time data, Order execution)          │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Core Components

- **BaseTrader**: Core trading engine handling data, orders, and position management
- **Strategy System**: Modular framework for unlimited custom strategies
- **Backtester**: High-fidelity simulation with synthetic tick generation
- **Broker Abstraction**: Clean MT5 integration with future broker support

## 📈 Strategies

### Built-in Strategies

| Strategy | Description | Best Timeframes | Features |
|----------|-------------|-----------------|----------|
| **Bollinger Bands** | Mean reversion & trend following | M5, M15, M30 | Volatility-based sizing, adaptive signals |
| **Breakout** | Zone-based breakout trading | M15, H1, H4 | Fixed zones, reversal detection |
| **Swarn** | Swarm agent lifecycle management | M1, M5, M15 | Win-streak doubling, graduation/death mechanics |

### Adding Custom Strategies

Creating a new strategy is simple - just implement the `BaseStrategy` interface:

```python
from strategies.base_strategy import BaseStrategy

class MyStrategy(BaseStrategy):
    def get_strategy_config(self):
        return {
            "BASE_LOT_SIZE": 0.01,
            "SL_POINTS": 5.0,
            "TP_POINTS": 10.0,
            # Your custom parameters
        }
    
    def initialize(self, current_price, historical_data=None):
        # Initialize your strategy
        pass
    
    def get_entry_signals(self, tick_price, prev_tick_price, current_time):
        # Your trading logic here
        signals = []
        if self._should_buy(tick_price):
            signals.append(("Buy", {"reason": "my_condition"}))
        return signals
    
    # Implement other required methods...

def get_strategy_instance(config):
    return MyStrategy(config)
```

**That's it!** Your strategy automatically works with:
- ✅ All timeframes (M1 to D1)
- ✅ Backtesting and live trading
- ✅ Risk management and position sizing
- ✅ Visualization and analysis
- ✅ Error handling and logging

## ⏱️ Multi-Timeframe Trading

Switch timeframes effortlessly without code changes:

```bash
# Scalping on 1-minute charts
python mainBot.py --backtest --strategy bollinger --timeframe M1

# Day trading on 15-minute charts  
python mainBot.py --backtest --strategy bollinger --timeframe M15

# Swing trading on 4-hour charts
python mainBot.py --backtest --strategy bollinger --timeframe H4

# Position trading on daily charts
python mainBot.py --backtest --strategy bollinger --timeframe D1
```

**Single Configuration**: One `TIMEFRAME` parameter controls all data operations across the entire system.

## 🔄 1:1 Backtest Consistency

Our backtesting engine produces **identical results** to live trading:

### Why It Works
- **Same Strategy Code**: Identical logic for backtest and live modes
- **Same Data Source**: Both use real MT5 historical data
- **Realistic Execution**: Proper bid/ask simulation with slippage modeling
- **Identical Risk Management**: Same position sizing, SL/TP calculations

### Expected Differences
- **Minimal Slippage**: ±0.1-0.5 points due to real market conditions
- **Execution Timing**: 1-3 second delays in live trading
- **Spread Impact**: Real spreads vs simulated execution

### Validation
```bash
# Validate consistency between modes
python validate_consistency.py --strategy bollinger --timeframe M15

# Monitor live execution quality
python mainBot.py --strategy bollinger --tick-stats --timeframe M15
```

## 📊 Advanced Features

### Real-Time Tick Monitoring
```bash
# Enable detailed tick analysis
python mainBot.py --strategy bollinger --tick-monitoring

# Standalone tick monitoring
python utils/tick_monitor.py --duration 60 --interval 0.5
```

**Provides**: Tick frequency, spread analysis, price volatility, market microstructure insights

### Risk Management
- **Position Limits**: Configurable maximum open positions
- **Profit Targets**: Daily profit goals with automatic stopping
- **Stop Losses**: Strategy-specific SL/TP calculations
- **Martingale Support**: Optional position size scaling

### Visualization
- **Interactive Charts**: Plotly-based professional charts
- **Trade Analysis**: Entry/exit points with P&L visualization
- **Performance Metrics**: Win rate, profit factor, drawdown analysis

## ⚙️ Configuration

### Basic Setup (`config.py`)
```python
class Config:
    # Trading Setup
    SYMBOL = "XAUUSD"
    TIMEFRAME = "M15"  # M1, M5, M15, M30, H1, H4, D1

    # Risk Management
    DAILY_PROFIT_TARGET = 500.0
    MAX_OPEN_POSITIONS = 3

    # MT5 Connection
    LOGIN = your_account_number
    PASSWORD = "your_password"
    SERVER = "your_server"
```

### Command Line Options
```bash
--strategy STRATEGY     # Choose strategy (bollinger, breakout, etc.)
--timeframe TIMEFRAME   # Set timeframe (M1, M5, M15, M30, H1, H4, D1)
--backtest             # Run backtest mode
--days DAYS            # Number of days to backtest
--tick-monitoring      # Enable tick monitoring
--tick-stats           # Show frequent tick statistics
```

## 🛡️ Production Ready

### Security
- Environment variable support for credentials
- Real trading safety validations
- Comprehensive error handling
- Automatic position management

### Performance
- Optimized for real-time trading
- Memory management with automatic cleanup
- Thread-safe operations
- Minimal latency tick processing

### Monitoring
- Detailed logging with multiple levels
- Real-time performance metrics
- Error classification and recovery
- Trade history and analysis

## 📚 Documentation

- **[Strategy Development](strategies/README.md)**: Complete strategy creation guide
- **[Timeframe Guide](TIMEFRAME_QUICK_REFERENCE.md)**: Multi-timeframe usage
- **[Tick Monitoring](TICK_MONITORING.md)**: Advanced market analysis
- **[Consistency Analysis](BACKTEST_VS_LIVE_ANALYSIS.md)**: Validation framework
- **[Changelog](CHANGELOG.md)**: Version history and updates

## 🤝 Contributing

1. Fork the repository
2. Create your strategy branch (`git checkout -b feature/my-strategy`)
3. Add your strategy following the `BaseStrategy` interface
4. Test with backtesting and validation tools
5. Update documentation and changelog
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This software is for educational and research purposes. Trading involves substantial risk of loss. Always test strategies thoroughly with demo accounts before live trading. The authors are not responsible for any financial losses.

---

**Ready to build your next trading strategy?** 🚀

[Get Started](#quick-start) | [View Strategies](#strategies) | [Read Docs](#documentation)
# MarioTehcnoto
