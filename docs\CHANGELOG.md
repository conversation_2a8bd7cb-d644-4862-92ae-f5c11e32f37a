# Changelog

All notable changes to the TradingBot project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [3.0.1] - 2025-01-15

### Fixed
- **Tick-based Trading**: Changed STRATEGY_INTERVAL from 15 seconds to 0.1 seconds for near real-time tick-based checking
- **Position Closure Recording**: Fixed critical issue where closed positions without immediate deal information were not properly recorded for martingale calculation
  - Added `_update_open_positions_profit()` method to track current profit for open positions
  - Modified position closure logic to use last known profit when deal information is delayed
  - Ensures martingale system correctly identifies wins/losses even when MT5 deal information is not immediately available
- **Martingale Accuracy**: Eliminated "Closed_No_Deals" status that was causing martingale to skip loss detection
- **Zone Reversal Logic**: Fixed incorrect reversal signal generation in breakout and swarn strategies
  - Removed incorrect "reversal signals" that generated trades when price bounced off zones
  - Clarified that "reversal" refers to zone type changes (resistance becomes support, support becomes resistance)
  - Only breakout signals now generate trades when price crosses through zones
  - Zone type reversal logic remains intact for proper support/resistance level management

### Changed
- **Trading Loop Performance**: Optimized main trading loop to check every tick instead of every 15 seconds
- **Position Profit Tracking**: Added continuous tracking of open position profits for accurate closure recording
- **Live Trading Output**: Cleaned up spammy tick statistics and replaced with useful strategy information
  - Removed per-tick price, spread, and volatility logging
  - Created extensible framework for strategy-specific display information
  - Added optional `get_display_info()` and `get_signal_description()` methods to BaseStrategy
  - Strategies can now customize their live trading output and signal descriptions
  - Increased strategy information logging interval from 5 minutes to 30 minutes (1800 seconds)
  - Added intelligent anti-spam logic: only shows strategy info when meaningfully changed
  - Added signal deduplication: prevents duplicate signals within 30 seconds
  - Enhanced logging with emojis and clear categorization (🎯 BREAKOUT, 🔄 REVERSAL, 📈 BOLLINGER, etc.)
  - Maintained clean separation between base trader and strategy-specific logic

## [3.0.0] - 2024-06-18

This major version represents a significant stability and reliability overhaul. All documented critical and high-priority bugs have been fixed, making the system more robust for both backtesting and live trading.

For a detailed summary of the analysis and all changes, see the [ANALYSIS_SUMMARY.md](ANALYSIS_SUMMARY.md) file.

### Fixed
- **Martingale Logic**: Corrected multiple critical bugs in the martingale system.
  - The calculation is now position-aware, preventing incorrect lot sizing in multi-position strategies.
  - Fixed a bug that caused the martingale sequence to reset improperly.
  - The system no longer crashes if a trade is missing a `close_time`.
- **Backtest Consistency**: The backtester's win/loss classification is now based on actual profit, making its logic identical to live trading and ensuring results are reliable.
- **Race Condition**: Fixed a race condition in the live trading position counter by implementing a proper thread-safe caching mechanism.
- **Stability**:
  - The `bollinger` strategy is now protected from division-by-zero errors.
  - The `breakout` strategy no longer consumes excessive memory by pre-generating zones.
- **"0 Trades" Bug**: The `breakout` strategy now generates zones dynamically around the current market price, fixing a bug where no trades would occur if the price was too far from the old hardcoded anchor.
- **Error Handling**:
  - The live trading engine will now automatically retry recoverable order-sending errors (e.g., requotes, timeouts).
  - The main application loop now has more specific exception handlers for better error diagnostics.

### Changed
- **Dependency Management**: Removed the `pymt5adapter` library to resolve a dependency conflict with `MetaTrader5`. The code has been reverted to use the `MetaTrader5` library directly.

### Removed
- **Analysis Files**: Deleted several stale analysis and bug report markdown files from the root directory, as all critical issues they documented have now been resolved. The summary of this work has been consolidated into `docs/ANALYSIS_SUMMARY.md`.

## [Unreleased]

### Added
- **Position-Aware Martingale System**: Enhanced martingale logic to track each position independently
  - Each position now maintains its own martingale sequence and losing streak
  - When one position loses, only that position's next trade will have increased lot size
  - Other positions continue with their default lot size regardless of other positions' performance
  - Added comprehensive logging to track martingale state for each position
  - Added `get_martingale_state_for_position()` method for debugging and monitoring
  - Created test script (`test_martingale.py`) to demonstrate position independence
  - Updated breakout strategy to support multiple positions (MAX_OPEN_POSITIONS = 3)

### Changed
- **Enhanced Martingale Logging**: Added detailed logging in both live trading and backtesting
  - Shows consecutive losses per position
  - Displays lot size calculations and reasoning
  - Helps verify position independence during operation

### Fixed
- **Multi-Position Martingale**: Fixed issue where martingale was affecting all positions globally
  - Now each position tracks its own losing streak independently
  - Prevents incorrect lot sizing when multiple positions are open
  - Ensures proper risk management per position

- **SwarnStrategy**: New sophisticated swarm-based trading strategy with agent lifecycle management
  - Win-streak based lot size doubling (0.01 → 0.02 → 0.04 → 0.08...)
  - Agent graduation at $100 profit and death at $0.30 loss
  - Virtual balance tracking for backtesting with agent restarts
  - Zone-based entry signals with fixed $0.30 take profit
  - Multi-generation agent lifecycle tracking
  - Full integration with TradingBot BaseStrategy architecture

## [2.0.0] - 2025-01-10

### GitHub Release Preparation
- **Repository Cleanup**: Comprehensive codebase cleanup for GitHub release
  - Removed unused imports (`os` from `config.py`)
  - Removed temporary test files (`test_timeframe.py`)
  - Cleaned up orphaned compiled Python files
  - Organized documentation structure

- **Professional Documentation Suite**:
  - Created comprehensive `README.md` with GitHub-ready formatting
  - Added badges, structured sections, and professional presentation
  - Created `CHANGELOG.md` for version tracking and change documentation
  - Added `LICENSE` file with MIT license and trading disclaimer
  - Organized all documentation with clear navigation and references
  - Verified `.gitignore` and `requirements.txt` are production-ready

### Code Quality Improvements
- **Import Cleanup**: Removed unused `os` import from `config.py`
- **File Organization**: Removed temporary test files superseded by validation framework
- **Documentation Structure**: Organized documentation hierarchy for easy navigation
- **Production Readiness**: All files prepared for GitHub release and public use

### Added
- **Multi-Timeframe Support**: Complete timeframe flexibility (M1, M5, M15, M30, H1, H4, D1)
  - Single `TIMEFRAME` configuration parameter controls all data operations
  - Command line `--timeframe` argument for easy switching
  - Automatic timeframe validation and mapping to MT5 constants
  - Updated visualization to use configured timeframe
  - Backward compatible with existing M1 default

- **Advanced Tick Monitoring System**: Real-time market microstructure analysis
  - Configurable tick history storage with memory management
  - Real-time statistics: frequency, volatility, spread analysis
  - Command line arguments: `--tick-monitoring`, `--tick-stats`
  - Standalone tick monitor utility (`utils/tick_monitor.py`)
  - Integration with live trading loop for performance monitoring

- **Enhanced Security for Real Trading**:
  - Environment variable support for sensitive credentials
  - Real trading configuration template (`config_real_trading.py`)
  - Safety validation warnings for risky configurations
  - Comprehensive real trading checklist and documentation

- **Validation and Testing Tools**:
  - Consistency validation script (`validate_consistency.py`)
  - Backtest vs live trading analysis framework
  - Comprehensive documentation for 1:1 trade consistency
  - Automated testing for signal generation and execution

- **Documentation Suite**:
  - `TIMEFRAME_IMPLEMENTATION.md`: Technical timeframe documentation
  - `TIMEFRAME_QUICK_REFERENCE.md`: User-friendly timeframe guide
  - `TICK_MONITORING.md`: Comprehensive tick monitoring documentation
  - `BACKTEST_VS_LIVE_ANALYSIS.md`: Consistency analysis and validation
  - `examples/timeframe_usage.py`: Demonstration script with examples

### Changed
- **BaseTrader Architecture**: Enhanced with timeframe and tick monitoring support
  - Added `_get_configured_timeframe()` method for dynamic timeframe handling
  - Updated `broker_get_historical_data()` to use configured timeframe as default
  - Enhanced `pull_ohlc()` method with flexible timeframe support
  - Integrated tick monitoring into live trading loop
  - Added comprehensive tick statistics and analysis methods

- **Configuration System**: Extended with new parameters
  - Added `TIMEFRAME` parameter with validation
  - Added tick monitoring configuration options
  - Enhanced CLI argument processing for new features
  - Improved validation with timeframe and safety checks

- **Strategy Initialization**: Updated to use configured timeframe
  - Modified `_initialize_strategy()` to use `pull_ohlc()` with configured timeframe
  - Maintained backward compatibility with existing strategies
  - All strategies now automatically adapt to any timeframe

- **Visualization System**: Updated for multi-timeframe support
  - Charts now display actual timeframe used
  - Dynamic chart titles showing timeframe information
  - Maintained compatibility with all existing chart features

### Fixed
- **Unused Import Cleanup**: Removed unused `os` import from `config.py`
- **Code Organization**: Cleaned up temporary test files and improved structure
- **Memory Management**: Enhanced tick monitoring with configurable history limits
- **Error Handling**: Improved error classification and recovery in live trading

### Removed
- **Temporary Files**: Cleaned up development and test files
  - Removed `test_timeframe.py` (functionality moved to `validate_consistency.py`)
  - Cleaned up orphaned compiled Python files
  - Removed redundant documentation files

### Technical Details
- **Architecture**: Maintained clean 3-tier architecture with enhanced flexibility
- **Performance**: Tick monitoring adds minimal overhead (<1% CPU impact)
- **Memory**: Configurable memory usage with automatic cleanup
- **Compatibility**: Full backward compatibility maintained
- **Testing**: Comprehensive validation framework for consistency verification

### Migration Guide
- **Existing Users**: No changes required - system defaults to M1 timeframe
- **New Features**: Add `--timeframe` parameter to use different timeframes
- **Real Trading**: Use `config_real_trading.py` template for secure setup
- **Monitoring**: Add `--tick-monitoring` for detailed market analysis

---

## [1.0.0] - 2024-12-XX

### Added
- Initial release of TradingBot with MT5 integration
- Multi-strategy architecture with BaseStrategy interface
- Three production strategies: Breakout, Bollinger Bands, Support/Resistance
- Comprehensive backtesting engine with synthetic tick generation
- Real-time trading capabilities with MT5 broker integration
- Position management with martingale support
- Risk management with profit targets and stop losses
- Visualization system with interactive charts
- Configuration management with validation
- Error handling and recovery mechanisms
- Logging system with multiple levels
- Documentation and examples

### Technical Foundation
- Clean 3-tier architecture: BaseTrader, Strategy System, Broker Abstraction
- Modular strategy system for unlimited extensibility
- Real MT5 data integration for backtesting and live trading
- Thread-safe operations with proper synchronization
- Memory management with trade history archiving
- Timezone handling with consistent datetime operations

---

## Development Guidelines

### Version Numbering
- **Major (X.0.0)**: Breaking changes, major architecture updates
- **Minor (X.Y.0)**: New features, strategy additions, significant enhancements
- **Patch (X.Y.Z)**: Bug fixes, minor improvements, documentation updates

### Change Categories
- **Added**: New features, strategies, tools, documentation
- **Changed**: Modifications to existing functionality
- **Deprecated**: Features marked for removal in future versions
- **Removed**: Deleted features, files, or functionality
- **Fixed**: Bug fixes and error corrections
- **Security**: Security-related improvements

### Documentation Requirements
- All major changes must include documentation updates
- New features require examples and usage instructions
- Breaking changes need migration guides
- Performance impacts should be documented
- Security implications must be noted

### Testing Requirements
- New features must include validation tests
- Strategy changes require backtest validation
- Performance changes need benchmarking
- Security features require penetration testing
- All changes must pass consistency validation
